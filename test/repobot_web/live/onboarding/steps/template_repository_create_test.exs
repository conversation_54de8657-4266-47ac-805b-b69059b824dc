defmodule RepobotWeb.Live.Onboarding.Steps.TemplateRepositoryCreateTest do
  use RepobotWeb.ConnCase, async: true
  use Repobot.Test.Fixtures

  import Mox
  import Phoenix.LiveViewTest

  setup :set_mox_from_context
  setup :verify_on_exit!

  describe "repository visibility toggle" do
    setup do
      user = create_user()

      # Enable private repos for the user's default organization so the toggle is shown
      {:ok, _org} =
        Repobot.Accounts.update_organization(user.default_organization, %{private_repos: true})

      # Reload user with updated organization
      user = Repobot.Repo.preload(user, :default_organization, force: true)

      # Mock GitHub client to avoid API calls during onboarding
      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> %Tentacat.Client{auth: %{access_token: "test_token"}} end)
      |> expect(:user_repos, fn _client, _login -> {:ok, []} end)

      {:ok, user: user}
    end

    test "displays correct labels for private and public repository states", %{
      conn: conn,
      user: user
    } do
      # Navigate to onboarding and get to the template repository creation step
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Step 1: Welcome - click Next
      view |> element("button", "Next") |> render_click()

      # Step 2: Template Repository - create new repository mode should be selected by default
      assert has_element?(view, "#create_new[checked]")

      # Debug: Print the current HTML to see what's being rendered
      html = render(view)
      IO.puts("Current HTML: #{html}")

      # Initially, the toggle should be in public mode (default is_private: false)
      assert has_element?(view, "span", "Public Repository")
      assert has_element?(view, "span", "(Visible to everyone)")
      refute has_element?(view, "span", "Private Repository")
      refute has_element?(view, "span", "(Only visible to you and your organization)")

      # Click the visibility toggle to switch to private
      view |> element("button[phx-click='toggle_visibility']") |> render_click()

      # Now it should show private repository labels
      assert has_element?(view, "span", "Private Repository")
      assert has_element?(view, "span", "(Only visible to you and your organization)")
      refute has_element?(view, "span", "Public Repository")
      refute has_element?(view, "span", "(Visible to everyone)")

      # Click the toggle again to switch back to public
      view |> element("button[phx-click='toggle_visibility']") |> render_click()

      # Should be back to public repository labels
      assert has_element?(view, "span", "Public Repository")
      assert has_element?(view, "span", "(Visible to everyone)")
      refute has_element?(view, "span", "Private Repository")
      refute has_element?(view, "span", "(Only visible to you and your organization)")
    end

    test "toggle switch visual state matches the labels", %{
      conn: conn,
      user: user
    } do
      # Navigate to onboarding and get to the template repository creation step
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Step 1: Welcome - click Next
      view |> element("button", "Next") |> render_click()

      # Step 2: Template Repository - create new repository mode should be selected by default
      assert has_element?(view, "#create_new[checked]")

      # Initially public - toggle should be in "off" position (aria-checked="false")
      toggle_button = view |> element("button[role='switch']")
      assert render(toggle_button) =~ ~s(aria-checked="false")
      assert has_element?(view, "span", "Public Repository")

      # Click to switch to private
      view |> element("button[phx-click='toggle_visibility']") |> render_click()

      # Now private - toggle should be in "on" position (aria-checked="true")
      toggle_button = view |> element("button[role='switch']")
      assert render(toggle_button) =~ ~s(aria-checked="true")
      assert has_element?(view, "span", "Private Repository")
    end
  end
end
